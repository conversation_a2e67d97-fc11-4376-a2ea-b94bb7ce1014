package jo.capitalbank.ms.library.exception;


import org.springframework.stereotype.Service;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
public class T24ErrorHandler {

    public Map<String, Object> handleT4ErrorResponse(String ofsResponse) {
        Map<String, Object> result = new LinkedHashMap<>();
        String[] parts = ofsResponse.split(",", 3);

        if (parts.length < 2) {
            throw new IllegalArgumentException("Invalid OFS response format");
        }

        String headerPart = parts[0];
        String fieldPart = parts.length == 2 ? parts[1] : parts[2]; // In some cases, header/fields are merged into parts[1]


/*        List<Map<String, Object>> headers = Arrays.stream(headerPart.split("/"))
                .map(header -> {
                    String[] split = header.split("::");
                    Map<String, Object> field = new LinkedHashMap<>();
                    field.put("name", split[0]);
                    field.put("value", split.length > 1 ? split[1] : split[0]);
                    return field;
                })
                .collect(Collectors.toList());*/

        List<Map<String, Object>> headers = new ArrayList<>();
        var headersMap = headerPart.split("/");
        var trxRef = new HashMap<String, Object>();
        trxRef.put("name","trxRef");
        trxRef.put("value", headersMap[0]);

        var msgId = new HashMap<String, Object>();
        msgId.put("name", "msgId");
        msgId.put("value", headersMap[1]);

        var status = new HashMap<String, Object>();
        status.put("name", "status");
        status.put("value", headersMap[2]);

        var operation = new HashMap<String, Object>();
        operation.put("name", "operation");
        operation.put("value", headersMap[3]);

        headers.add(trxRef);
        headers.add(msgId);
        headers.add(status);
        headers.add(operation);
        result.put("headers", headers);

        // =========================
        // 2. Parse Body Fields and Errors
        // =========================
        String[] fields = fieldPart.split(",");
        Map<String, List<Map<String, Object>>> groupedByRecord = new TreeMap<>();
        List<Map<String, Object>> errors = new ArrayList<>();

        Pattern pattern = Pattern.compile("^(.*?):(\\d+):(\\d+)$");

        for (String field : fields) {
            String[] keyValue = field.split("=", 2);
            String rawKey = keyValue[0].trim();
            String value = keyValue.length > 1 ? keyValue[1].trim() : null;

            Matcher matcher = pattern.matcher(rawKey);
            if (!matcher.find()) continue;

            String recordIndex = matcher.group(3);

            Map<String, Object> t24Field = new LinkedHashMap<>();
            t24Field.put("name", rawKey);
            t24Field.put("value", (value == null || value.isBlank()) ? null : value);

            // Add to body (grouped by record index)
            Map<String, Object> wrapper = new LinkedHashMap<>();
            wrapper.put("t24field", t24Field);
            groupedByRecord.computeIfAbsent(recordIndex, k -> new ArrayList<>()).add(wrapper);

            // Add to error array
            Map<String, Object> errorField = new LinkedHashMap<>();
            errorField.put("field", rawKey);
            errorField.put("message", value);
            errors.add(errorField);
        }

        // =========================
        // 3. Transform to body[]
        // =========================
        List<Map<String, Object>> response = groupedByRecord.entrySet().stream()
                .map(entry -> Map.of(
                        "recordIndex", entry.getKey(),
                        "fields", entry.getValue()
                ))
                .collect(Collectors.toList());

        result.put("body", response);

        // =========================
        // 4. Add array of error fields
        // =========================
        result.put("error", errors);

        return result;
    }
}
