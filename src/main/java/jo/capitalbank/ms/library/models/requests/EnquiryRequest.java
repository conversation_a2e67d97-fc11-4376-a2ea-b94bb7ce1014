package jo.capitalbank.ms.library.models.requests;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class EnquiryRequest {
    private String options;
    private String operation;
    private UserInformation userInformation;
    private String enquiry;
    private String enquiryId;
    private List<T24Field> t24Fields;
}
