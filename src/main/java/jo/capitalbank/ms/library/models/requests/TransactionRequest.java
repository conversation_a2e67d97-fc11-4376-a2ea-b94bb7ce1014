package jo.capitalbank.ms.library.models.requests;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TransactionRequest {
    private String operation;
    private TransactionOptions options;
    private UserInformation userInformation;
    private String transactionId;
    private List<T24Field> t24Fields;
    
    @Data
    public static class TransactionOptions {
        private String version;
        private String function;
        private String processOrValidate;
        private String gtsControl;
        private String noOfAuth;
    }
}
