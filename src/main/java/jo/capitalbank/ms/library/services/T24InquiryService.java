package jo.capitalbank.ms.library.services;


import jakarta.jms.Connection;
import jakarta.jms.TextMessage;
import jo.capitalbank.ms.library.configurations.EnquiryManager;
import jo.capitalbank.ms.library.models.requests.EnquiryRequest;
import lombok.AllArgsConstructor;
import lombok.RequiredArgsConstructor;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.jms.core.JmsTemplate;
import org.springframework.stereotype.Service;


import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import jakarta.jms.Connection;
import jakarta.jms.ConnectionFactory;
import jakarta.jms.JMSException;
import jakarta.jms.Message;
import jakarta.jms.MessageConsumer;
import jakarta.jms.MessageProducer;
import jakarta.jms.Queue;
import jakarta.jms.Session;
import jakarta.jms.TextMessage;

@Service

@Slf4j
public class T24InquiryService {


    @Value("${ibm.mq.enquiry.queueRequest}")
    private String queueNameRequest;
    @Value("${ibm.mq.enquiry.queueResponse}")
    private String queueNameResponse;

    @Autowired
    private EnquiryManager enquiryManager;

    @Autowired
    private JmsTemplate jmsTemplate;

    private final ConnectionFactory connectionFactory;

    public T24InquiryService(@Qualifier("cachingConnectionFactory") ConnectionFactory connectionFactory) {
        this.connectionFactory = connectionFactory;
    }

    public ResponseEntity<?> processEnquiry(EnquiryRequest request) {
        String ofsMessage = convertEnquiryToOFS(request);
        String correlationId = UUID.randomUUID().toString();
        CompletableFuture<String> future = new CompletableFuture<>();

        log.info("Sending OFS message with Correlation ID {}: {}", correlationId, ofsMessage);

        try {
            // Register future before sending (async pattern)
            enquiryManager.register(correlationId, future);

            // Send message using JmsTemplate (uses cached producers)
            jmsTemplate.send(queueNameRequest, session -> {
                TextMessage message = session.createTextMessage(ofsMessage);
                message.setJMSCorrelationID(correlationId);
                message.setJMSDeliveryMode(DeliveryMode.PERSISTENT); // Persistent delivery
                return message;
            });

            // Wait for async response with timeout
            String replyText = future.get(30, TimeUnit.SECONDS);
            log.info("Received reply for Correlation ID {}: {}", correlationId, replyText);
            return ResponseEntity.ok(convertOfsToJson(replyText));

        } catch (TimeoutException e) {
            log.warn("Timeout waiting for response with Correlation ID: {}", correlationId);
            enquiryManager.cleanup(correlationId);
            return ResponseEntity.status(HttpStatus.REQUEST_TIMEOUT)
                    .body("Request timeout - no response received within 30 seconds");
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("Thread interrupted while waiting for response", e);
            enquiryManager.cleanup(correlationId);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Request interrupted");
        } catch (ExecutionException e) {
            log.error("Error while processing message with Correlation ID: {}", correlationId, e);
            enquiryManager.cleanup(correlationId);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Internal error occurred while processing the request");
        } catch (Exception e) {
            log.error("Unexpected error while sending message", e);
            enquiryManager.cleanup(correlationId);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Internal error occurred while processing the request");
        }
    }

  /*public ResponseEntity<?> processEnquiry(EnquiryRequest request) {
        String ofsMessage = convertEnquiryToOFS(request);
        String correlationId = UUID.randomUUID().toString();
        CompletableFuture<String> future = new CompletableFuture<>();

        // Register future before sending
        correlationManager.register(correlationId, future);

        log.info("ofs message : {} ", ofsMessage);
        jmsTemplate.send(queueNameRequest, session -> {
            TextMessage message = session.createTextMessage(ofsMessage);
            message.setJMSCorrelationID(correlationId);
            var tempQueue = session.createTemporaryQueue();
            message.setJMSReplyTo(tempQueue);
            //message.setJMSReplyTo(session.createQueue(queueNameResponse));
            return message;
        });


        try {
            String reply = future.get(10, TimeUnit.SECONDS);
            convertOfsToJson(reply);
            log.info("Reply: {}", reply);
            return new ResponseEntity<>(convertOfsToJson(reply), HttpStatus.OK);
        } catch (TimeoutException | InterruptedException | ExecutionException e) {
            correlationManager.complete(correlationId, null);
            return new ResponseEntity<>("No response received in time", HttpStatus.SERVICE_UNAVAILABLE);
        }


    }*/


    private String convertEnquiryToOFS(EnquiryRequest request) {
        StringBuilder ofs = new StringBuilder();

        var operation = "ENQUIRY.SELECT";
        if (request.getOperation() != null && !(request.getOperation().isEmpty()))
            operation = request.getOperation();

        ofs.append(operation + ",");

        ofs.append(",");
        if (!request.getUserInformation().getUsername().isEmpty())
            ofs.append(request.getUserInformation().getUsername());
        ofs.append(",");
        ofs.append(request.getEnquiry()).append(",");
        if (!request.getEnquiryId().isEmpty())
            ofs.append(request.getEnquiryId()).append(",");

        if (request.getT24Fields() != null) {
            String fields = request.getT24Fields().stream().map(field -> field.getName() + "=" + field.getValue()).collect(Collectors.joining(","));

            ofs.append(fields);
        }

        return ofs.toString();
    }

    public Map<String, Object> convertOfsToJson(String ofsResponse) {
        try {
            log.info("ofs message response :{}", ofsResponse);
            return parseResponse(ofsResponse);
        } catch (Exception e) {
            log.error("Error converting OFS response to JSON. OFS response: {}", ofsResponse, e);
            return new LinkedHashMap<>();
        }
    }

    public Map<String, Object> parseResponse(String ofsResponse) {
        Map<String, Object> result = new LinkedHashMap<>();

        String[] parts = ofsResponse.split(",", 3);
        if (parts.length < 3) throw new IllegalArgumentException("Invalid OFS format");

        String headerPart = parts[1];

        // Parse headers
        List<Map<String, Object>> headerList = Arrays.stream(headerPart.split("/"))
                .map(header -> {
                    String[] split = header.split("::");
                    Map<String, Object> field = new LinkedHashMap<>();
                    field.put("name", split[0]);
                    field.put("value", split.length > 1 ? split[1] : split[0]);
                    return field;
                })
                .collect(Collectors.toList());

        log.info("Parsing headers: " + headerList);
        result.put("headers", headerList);

        // 2. Parse Response Section
        // =========================
        String[] fields = parts[2].split(",");
        Map<String, List<Map<String, Object>>> grouped = new LinkedHashMap<>();

        Pattern pattern = Pattern.compile("^(.*?):1:(\\d+)$");

        for (String field : fields) {
            String[] keyValue = field.split("=", 2);
            String fullKey = keyValue[0].trim();
            String value = (keyValue.length == 2) ? keyValue[1].trim() : null;

            Matcher matcher = pattern.matcher(fullKey);
            if (!matcher.find()) continue;

            String recordIndex = matcher.group(2);

            Map<String, Object> innerField = new LinkedHashMap<>();
            innerField.put("name", fullKey);
            innerField.put("value", (value == null || value.isBlank()) ? null : value);

            Map<String, Object> t24field = new LinkedHashMap<>();
            t24field.put("t24field", innerField);

            grouped.computeIfAbsent(recordIndex, k -> new ArrayList<>()).add(t24field);
        }

        // =========================
        // 3. Transform to List of Records
        // =========================
        List<Map<String, Object>> response = grouped.entrySet().stream()
                .map(entry -> Map.of(
                        "recordIndex", entry.getKey(),
                        "fields", entry.getValue()
                ))
                .collect(Collectors.toList());

        result.put("body", response);

        return result;
    }


}
