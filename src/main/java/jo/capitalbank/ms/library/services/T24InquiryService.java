package jo.capitalbank.ms.library.services;


import jakarta.jms.Connection;
import jakarta.jms.TextMessage;
import jo.capitalbank.ms.library.configurations.EnquiryManager;
import jo.capitalbank.ms.library.models.requests.EnquiryRequest;
import lombok.AllArgsConstructor;
import lombok.RequiredArgsConstructor;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.jms.core.JmsTemplate;
import org.springframework.stereotype.Service;


import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import jakarta.jms.Connection;
import jakarta.jms.ConnectionFactory;
import jakarta.jms.JMSException;
import jakarta.jms.Message;
import jakarta.jms.MessageConsumer;
import jakarta.jms.MessageProducer;
import jakarta.jms.Queue;
import jakarta.jms.Session;
import jakarta.jms.TextMessage;

@Service

@Slf4j
public class T24InquiryService {


    @Value("${ibm.mq.enquiry.queueRequest}")
    private String queueNameRequest;
    @Value("${ibm.mq.enquiry.queueResponse}")
    private String queueNameResponse;
    private final ConnectionFactory connectionFactory;

    public T24InquiryService(@Qualifier("cachingConnectionFactory") ConnectionFactory connectionFactory) {
        this.connectionFactory = connectionFactory;
    }

    public ResponseEntity<?> processEnquiry(EnquiryRequest request) {
        String ofsMessage = convertEnquiryToOFS(request);
        String correlationId = UUID.randomUUID().toString();

        log.info("Sending OFS message with Correlation ID {}: {}", correlationId, ofsMessage);

        try (
                Connection connection = connectionFactory.createConnection();
                Session session = connection.createSession(false, Session.AUTO_ACKNOWLEDGE)
        ) {
            connection.start();

            Queue requestQueue = session.createQueue(queueNameRequest);
            Queue responseQueue = session.createQueue(queueNameResponse);

            // Create message
            TextMessage message = session.createTextMessage(ofsMessage);
            message.setJMSCorrelationID(correlationId);
            message.setJMSReplyTo(responseQueue); // Optional, since T24 uses a fixed response queue

            // Send message
            MessageProducer producer = session.createProducer(requestQueue);
            producer.send(message);

            // Receive response with selector
            String selector = "JMSCorrelationID = '" + correlationId + "'";
            MessageConsumer consumer = session.createConsumer(responseQueue, selector);
            Message reply = consumer.receive(10000); // 10 seconds timeout

            if (reply instanceof TextMessage textMessage) {
                String replyText = textMessage.getText();
                log.info("Received reply for Correlation ID {}: {}", correlationId, replyText);
                return ResponseEntity.ok(convertOfsToJson(replyText));
            } else {
                log.warn("No valid text message received within timeout");
                return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE)
                        .body("No valid response received");
            }

        } catch (Exception e) {
            log.error("Error while sending/receiving message", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Internal error occurred while processing the request");
        }
    }

  /*public ResponseEntity<?> processEnquiry(EnquiryRequest request) {
        String ofsMessage = convertEnquiryToOFS(request);
        String correlationId = UUID.randomUUID().toString();
        CompletableFuture<String> future = new CompletableFuture<>();

        // Register future before sending
        correlationManager.register(correlationId, future);

        log.info("ofs message : {} ", ofsMessage);
        jmsTemplate.send(queueNameRequest, session -> {
            TextMessage message = session.createTextMessage(ofsMessage);
            message.setJMSCorrelationID(correlationId);
            var tempQueue = session.createTemporaryQueue();
            message.setJMSReplyTo(tempQueue);
            //message.setJMSReplyTo(session.createQueue(queueNameResponse));
            return message;
        });


        try {
            String reply = future.get(10, TimeUnit.SECONDS);
            convertOfsToJson(reply);
            log.info("Reply: {}", reply);
            return new ResponseEntity<>(convertOfsToJson(reply), HttpStatus.OK);
        } catch (TimeoutException | InterruptedException | ExecutionException e) {
            correlationManager.complete(correlationId, null);
            return new ResponseEntity<>("No response received in time", HttpStatus.SERVICE_UNAVAILABLE);
        }


    }*/


    private String convertEnquiryToOFS(EnquiryRequest request) {
        StringBuilder ofs = new StringBuilder();

        var operation = "ENQUIRY.SELECT";
        if (request.getOperation() != null && !(request.getOperation().isEmpty()))
            operation = request.getOperation();

        ofs.append(operation + ",");

        ofs.append(",");
        if (!request.getUserInformation().getUsername().isEmpty())
            ofs.append(request.getUserInformation().getUsername());
        ofs.append(",");
        ofs.append(request.getEnquiry()).append(",");
        if (!request.getEnquiryId().isEmpty())
            ofs.append(request.getEnquiryId()).append(",");

        if (request.getT24Fields() != null) {
            String fields = request.getT24Fields().stream().map(field -> field.getName() + "=" + field.getValue()).collect(Collectors.joining(","));

            ofs.append(fields);
        }

        return ofs.toString();
    }

    public Map<String, Object> convertOfsToJson(String ofsResponse) {
        try {
            log.info("ofs message response :{}", ofsResponse);
            return parseResponse(ofsResponse);
        } catch (Exception e) {
            log.error("Error converting OFS response to JSON. OFS response: {}", ofsResponse, e);
            return new LinkedHashMap<>();
        }
    }

    public Map<String, Object> parseResponse(String ofsResponse) {
        Map<String, Object> result = new LinkedHashMap<>();

        String[] parts = ofsResponse.split(",", 3);
        if (parts.length < 3) throw new IllegalArgumentException("Invalid OFS format");

        String headerPart = parts[1];

        // Parse headers
        List<Map<String, Object>> headerList = Arrays.stream(headerPart.split("/"))
                .map(header -> {
                    String[] split = header.split("::");
                    Map<String, Object> field = new LinkedHashMap<>();
                    field.put("name", split[0]);
                    field.put("value", split.length > 1 ? split[1] : split[0]);
                    return field;
                })
                .collect(Collectors.toList());

        log.info("Parsing headers: " + headerList);
        result.put("headers", headerList);

        // 2. Parse Response Section
        // =========================
        String[] fields = parts[2].split(",");
        Map<String, List<Map<String, Object>>> grouped = new LinkedHashMap<>();

        Pattern pattern = Pattern.compile("^(.*?):1:(\\d+)$");

        for (String field : fields) {
            String[] keyValue = field.split("=", 2);
            String fullKey = keyValue[0].trim();
            String value = (keyValue.length == 2) ? keyValue[1].trim() : null;

            Matcher matcher = pattern.matcher(fullKey);
            if (!matcher.find()) continue;

            String recordIndex = matcher.group(2);

            Map<String, Object> innerField = new LinkedHashMap<>();
            innerField.put("name", fullKey);
            innerField.put("value", (value == null || value.isBlank()) ? null : value);

            Map<String, Object> t24field = new LinkedHashMap<>();
            t24field.put("t24field", innerField);

            grouped.computeIfAbsent(recordIndex, k -> new ArrayList<>()).add(t24field);
        }

        // =========================
        // 3. Transform to List of Records
        // =========================
        List<Map<String, Object>> response = grouped.entrySet().stream()
                .map(entry -> Map.of(
                        "recordIndex", entry.getKey(),
                        "fields", entry.getValue()
                ))
                .collect(Collectors.toList());

        result.put("body", response);

        return result;
    }


}
