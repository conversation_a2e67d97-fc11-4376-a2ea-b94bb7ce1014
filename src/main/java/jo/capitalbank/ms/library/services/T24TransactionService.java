package jo.capitalbank.ms.library.services;



import jakarta.jms.*;
import jakarta.jms.Queue;
import jo.capitalbank.ms.library.configurations.EnquiryManager;
import jo.capitalbank.ms.library.exception.T24ErrorHandler;
import jo.capitalbank.ms.library.models.requests.TransactionRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.jms.core.JmsTemplate;
import org.springframework.stereotype.Service;


import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
@Slf4j
public class T24TransactionService {



    @Autowired
    private T24ErrorHandler errorHandler;

    @Autowired
    private EnquiryManager enquiryManager;

    @Autowired
    private JmsTemplate jmsTemplate;

    @Value("${ibm.mq.transaction.queueRequest}")
    private String queueNameRequest;
    @Value("${ibm.mq.transaction.queueResponse}")
    private String queueNameResponse;

    private final ConnectionFactory connectionFactory;

    public T24TransactionService(@Qualifier("cachingConnectionFactory") ConnectionFactory connectionFactory) {
        this.connectionFactory = connectionFactory;
    }





    public ResponseEntity<?> processTransaction(TransactionRequest request) throws JMSException {
        String ofsMessage = convertTransactionToOFS(request);
        String correlationId = UUID.randomUUID().toString();
        CompletableFuture<String> future = new CompletableFuture<>();

        log.info("Sending OFS message with Correlation ID {}: {}", correlationId, ofsMessage);

        try {
            // Register future before sending (async pattern)
            enquiryManager.register(correlationId, future);

            // Send message using JmsTemplate (uses cached producers)
            jmsTemplate.send(queueNameRequest, session -> {
                TextMessage message = session.createTextMessage(ofsMessage);
                message.setJMSCorrelationID(correlationId);
                message.setJMSDeliveryMode(DeliveryMode.PERSISTENT); // Persistent delivery
                return message;
            });

            // Wait for async response with timeout
            String replyText = future.get(30, TimeUnit.SECONDS);
            log.info("Received reply for Correlation ID {}: {}", correlationId, replyText);
            return ResponseEntity.ok(convertOfsTrxToJson(replyText));

        } catch (TimeoutException e) {
            log.warn("Timeout waiting for response with Correlation ID: {}", correlationId);
            enquiryManager.cleanup(correlationId); // Clean up pending future
            return ResponseEntity.status(HttpStatus.REQUEST_TIMEOUT)
                    .body("Request timeout - no response received within 30 seconds");
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("Thread interrupted while waiting for response", e);
            enquiryManager.cleanup(correlationId);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Request interrupted");
        } catch (ExecutionException e) {
            log.error("Error while processing message with Correlation ID: {}", correlationId, e);
            enquiryManager.cleanup(correlationId);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Internal error occurred while processing the request");
        } catch (Exception e) {
            log.error("Unexpected error while sending message", e);
            enquiryManager.cleanup(correlationId);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Internal error occurred while processing the request");
        }
    }

    private String convertTransactionToOFS(TransactionRequest request) {
        StringBuilder ofs = new StringBuilder();

        // Add operation
        ofs.append(request.getOperation()).append(",");

        // Add version and function
        ofs.append(request.getOptions().getVersion()).append("/").append(request.getOptions().getFunction()).append("/").append(request.getOptions().getProcessOrValidate()).append("//").append(request.getOptions().getNoOfAuth()).append(",");

        // Add user information
        if (!request.getUserInformation().getUsername().isEmpty()) {
            ofs.append(request.getUserInformation().getUsername()).append(",");
        } else {
            ofs.append("IIBUSER01");
        }
        if (request.getUserInformation().getPassword() != null && !request.getUserInformation().getPassword().isEmpty()) {
            ofs.append(request.getUserInformation().getPassword()).append(",");
        } else {
            ofs.append("/");
        }

        if (request.getUserInformation().getCompany() != null && !request.getUserInformation().getCompany().isEmpty()) {
            ofs.append(request.getUserInformation().getCompany()).append(",");
        } else {
            ofs.append("/,");
        }

        ofs.append(request.getTransactionId()).append(",");
        // Add transaction fields
        String fields = request.getT24Fields().stream().map(field -> field.getName() + "=" + field.getValue()).collect(Collectors.joining(","));

        ofs.append(fields);

        return ofs.toString();
    }

    public Map<String, Object> convertOfsTrxToJson(String ofsResponse) {
        try {
            return parseTrxResponse(ofsResponse);
        } catch (Exception e) {
            log.error("Error converting OFS response to JSON. OFS response: {}", ofsResponse, e);
            return new LinkedHashMap<>();
        }
    }


    public Map<String, Object> parseTrxResponse(String ofsResponse) {
        Map<String, Object> result = new LinkedHashMap<>();

        if (ofsResponse == null || ofsResponse.trim().isEmpty()) {
            throw new IllegalArgumentException("OFS response is empty or null.");
        }

        // Split the OFS response: skip trx info (parts[0]), take headers and field values
        String[] parts = ofsResponse.split(",", 2);
        log.info("Parsing trx response: {}", Arrays.toString(parts));
        String[] errorHeader = parts[0].split("/");

        log.info("Parsing error header: {}", Arrays.toString(errorHeader));
        if (errorHeader[2].equals("-1") || errorHeader[2].equals("-2") || errorHeader[2].equals("-3")) {

            return errorHandler.handleT4ErrorResponse(ofsResponse);

        } else {

            String headerPart = parts[0];
            String fieldPart = parts[1];

            // =========================
            // 1. Parse Headers
            // =========================
/*            List<Map<String, Object>> headers = Arrays.stream(headerPart.split("/"))
                    .map(header -> {
                        String[] split = header.split("::");
                        Map<String, Object> field = new LinkedHashMap<>();
                        field.put("name", split[0]);
                        field.put("value", split.length > 1 ? split[1] : split[0]);
                        return field;
                    })
                    .collect(Collectors.toList());*/

            List<Map<String, Object>> headers = new ArrayList<>();
            var headersMap = headerPart.split("/");
            var trxRef = new HashMap<String, Object>();
            trxRef.put("name","trxRef");
            trxRef.put("value", headersMap[0]);

            var msgId = new HashMap<String, Object>();
            msgId.put("name", "msgId");
            msgId.put("value", headersMap[1]);

            var status = new HashMap<String, Object>();
            status.put("name", "status");
            status.put("value", headersMap[2]);

            var operation = new HashMap<String, Object>();
            operation.put("name", "operation");
            operation.put("value", headersMap[3]);

            headers.add(trxRef);
            headers.add(msgId);
            headers.add(status);
            headers.add(operation);

            result.put("headers", headers);

            // =========================
            // 2. Parse Body Fields
            // =========================
            String[] fields = fieldPart.split(",");
            Map<String, List<Map<String, Object>>> groupedByRecord = new TreeMap<>(); // recordIndex -> list of t24field

            Pattern pattern = Pattern.compile("^(.*?)(?::(\\d+))?(?::(\\d+))?$");

            for (String field : fields) {
                String[] keyValue = field.split("=", 2);
                String rawKey = keyValue[0].trim();
                String value = keyValue.length > 1 ? keyValue[1].trim() : null;

                Matcher matcher = pattern.matcher(rawKey);


                Map<String, Object> t24Field = new LinkedHashMap<>();
                t24Field.put("name", rawKey);
                t24Field.put("value", (value == null || value.isBlank()) ? null : value);

                Map<String, Object> wrapper = Map.of("t24field", t24Field);

/*                if (matcher.matches()) {
                    String recordIndex = matcher.group(3) != null ? matcher.group(3) : "1";
                    groupedByRecord.computeIfAbsent(recordIndex, k -> new ArrayList<>()).add(wrapper);
                } else {
                    // fallback, should never hit here now
                    groupedByRecord.computeIfAbsent("1", k -> new ArrayList<>()).add(wrapper);
                }*/

                groupedByRecord
                        .computeIfAbsent("1", k -> new ArrayList<>())
                        .add(wrapper);
            }


            // =========================
            // 3. Transform to response[]
            // =========================
            List<Map<String, Object>> response = groupedByRecord.entrySet().stream()
                    .map(entry -> Map.of(
                            "recordIndex", entry.getKey(),
                            "fields", entry.getValue()
                    ))
                    .collect(Collectors.toList());

            result.put("body", response);


        }
        return result;
    }

}
