package jo.capitalbank.ms.library.configurations;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Component
public class EnquiryManager {
    private final Map<String, CompletableFuture<String>> correlationMap = new ConcurrentHashMap<>();

    public void register(String correlationId, CompletableFuture<String> future) {
        correlationMap.put(correlationId, future);
    }

    public void complete(String correlationId, String response) {
        log.info("CorrelationId: {}, response: {}", correlationId, response);
        CompletableFuture<String> future = correlationMap.remove(correlationId);
        if (future != null) {
            log.info("CorrelationId: {}, future: {}", correlationId, future);
            future.complete(response);
        }
    }

    public void cleanup(String correlationId) {
        CompletableFuture<String> future = correlationMap.remove(correlationId);
        if (future != null) {
            log.warn("Cleaning up pending future for correlationId: {}", correlationId);
            future.cancel(true);
        }
    }
}
