package jo.capitalbank.ms.library.configurations;

import com.ibm.msg.client.jakarta.wmq.WMQConstants;

import jakarta.jms.ConnectionFactory;
import jakarta.jms.JMSException;
import jakarta.jms.Session;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.ibm.mq.jakarta.jms.MQQueueConnectionFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jms.config.DefaultJmsListenerContainerFactory;
import org.springframework.jms.connection.CachingConnectionFactory;
import org.springframework.jms.core.JmsTemplate;


@Configuration
@Slf4j
public class MQConfig {
    
    @Value("${ibm.mq.host}")
    private String host;
    
    @Value("${ibm.mq.port}")
    private Integer port;
    
    @Value("${ibm.mq.queueManager}")
    private String queueManager;
    
    @Value("${ibm.mq.channel}")
    private String channel;
    
    @Value("${ibm.mq.transaction.queueRequest}")
    private String queueRequest;


    @Bean
    public MQQueueConnectionFactory mqQueueConnectionFactory() throws JMSException, jakarta.jms.JMSException {
        MQQueueConnectionFactory factory = new MQQueueConnectionFactory();
        factory.setHostName(host);
        factory.setPort(port);
        factory.setQueueManager(queueManager);
        factory.setChannel(channel);
        factory.setTransportType(WMQConstants.WMQ_CM_CLIENT);

        // Enable persistent delivery for reliability
        factory.setBooleanProperty(WMQConstants.WMQ_MQMD_MESSAGE_PERSISTENCE, true);

        return factory;
    }
    @Bean
    public CachingConnectionFactory cachingConnectionFactory(MQQueueConnectionFactory mqQueueConnectionFactory) {
        CachingConnectionFactory cachingConnectionFactory = new CachingConnectionFactory();
        cachingConnectionFactory.setTargetConnectionFactory(mqQueueConnectionFactory);

        // Increase cache sizes for better performance under load
        cachingConnectionFactory.setSessionCacheSize(50); // Increased from 10
        cachingConnectionFactory.setCacheProducers(true); // Cache producers
        cachingConnectionFactory.setCacheConsumers(false); // Don't cache consumers (we use listeners)

        return cachingConnectionFactory;
    }
    
    @Bean
    public JmsTemplate jmsTemplate(CachingConnectionFactory cachingConnectionFactory) {
        JmsTemplate jmsTemplate = new JmsTemplate(cachingConnectionFactory);
        jmsTemplate.setDefaultDestinationName(queueRequest);

        // Configure for persistent delivery and better performance
        jmsTemplate.setDeliveryPersistent(true);
        jmsTemplate.setExplicitQosEnabled(true);
        jmsTemplate.setReceiveTimeout(30000L); // 30 seconds

        return jmsTemplate;
    }

    @Bean
    public DefaultJmsListenerContainerFactory jmsListenerContainerFactory(@Qualifier("cachingConnectionFactory") ConnectionFactory connectionFactory) {
        DefaultJmsListenerContainerFactory factory = new DefaultJmsListenerContainerFactory();
        factory.setConnectionFactory(connectionFactory);
        factory.setMessageConverter(null);
        factory.setReceiveTimeout(30000L); // Reduced from 60s

        // Increased concurrency for production load (4-16 as suggested)
        factory.setConcurrency("4-16");

        // Enable session transacted for reliability
        factory.setSessionTransacted(false); // Keep false for AUTO_ACKNOWLEDGE
        factory.setSessionAcknowledgeMode(Session.AUTO_ACKNOWLEDGE);

        // Configure error handling
        factory.setErrorHandler(t -> log.error("JMS Listener error", t));

        return factory;
    }


}
