package jo.capitalbank.ms.library.configurations;

import jakarta.jms.JMSException;
import jakarta.jms.Message;
import jakarta.jms.TextMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jms.annotation.JmsListener;
import org.springframework.stereotype.Component;



@Slf4j
public class MQListener {



   private final EnquiryManager enquiryManager;

    public MQListener(EnquiryManager enquiryManager) {
        this.enquiryManager = enquiryManager;
    }

    @JmsListener(destination = "${ibm.mq.enquiry.queueResponse}",containerFactory = "jmsListenerContainerFactory")
    public void receiveReply(Message message) {
        try {
            String correlationId = message.getJMSCorrelationID();
            if (message instanceof TextMessage) {
                String body = ((TextMessage) message).getText();
                log.info("Received reply with correlation ID {}: {}", correlationId, body);
                enquiryManager.complete(correlationId, body);
                // Use correlationId to match to the request, e.g., update a map, notify a thread, etc.
            }
        } catch (JMSException e) {
            log.error("Error processing reply message", e);
        }
    }


    @JmsListener(destination = "${ibm.mq.transaction.queueResponse}")
    public void receiveReplyTxn(Message message) {
        try {
            String correlationId = message.getJMSCorrelationID();
            if (message instanceof TextMessage) {
                String body = ((TextMessage) message).getText();
                log.info("Received reply with correlation ID {}: {}", correlationId, body);
                enquiryManager.complete(correlationId, body);
                // Use correlationId to match to the request, e.g., update a map, notify a thread, etc.
            }
        } catch (JMSException e) {
            log.error("Error processing reply message", e);
        }
    }
}
