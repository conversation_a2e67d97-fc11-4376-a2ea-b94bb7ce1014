# IBM MQ Configuration
ibm.mq.host=your-mq-host
ibm.mq.port=1414
ibm.mq.queueManager=YOUR_QM
ibm.mq.channel=YOUR_CHANNEL

# Queue Names
ibm.mq.transaction.queueRequest=T24.TRANSACTION.REQUEST
ibm.mq.transaction.queueResponse=T24.TRANSACTION.RESPONSE
ibm.mq.enquiry.queueRequest=T24.ENQUIRY.REQUEST
ibm.mq.enquiry.queueResponse=T24.ENQUIRY.RESPONSE

# Spring Boot Optimizations for Production
spring.task.execution.pool.core-size=10
spring.task.execution.pool.max-size=50
spring.task.execution.pool.queue-capacity=100
spring.task.execution.thread-name-prefix=t24-async-

# JMS/MQ Performance Settings
spring.jms.listener.concurrency=4-16
spring.jms.listener.receive-timeout=30000
spring.jms.template.delivery-persistent=true
spring.jms.template.receive-timeout=30000

# Connection Pool Settings
spring.jms.cache.enabled=true
spring.jms.cache.session-cache-size=50

# Logging for monitoring
logging.level.jo.capitalbank.ms.library=INFO
logging.level.org.springframework.jms=WARN
logging.level.com.ibm.mq=WARN

# Health check and monitoring
management.endpoints.web.exposure.include=health,metrics,prometheus
management.endpoint.health.show-details=always
management.metrics.export.prometheus.enabled=true
